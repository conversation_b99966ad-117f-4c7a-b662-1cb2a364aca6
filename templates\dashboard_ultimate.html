<!DOCTYPE html>
<html lang="pl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>🚀 Ultimate Trading Dashboard - Discord Bybit Monitor</title>

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#0a0e1a" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta
      name="apple-mobile-web-app-status-bar-style"
      content="black-translucent"
    />
    <link rel="manifest" href="/static/manifest.json" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600&display=swap"
      rel="stylesheet"
    />

    <!-- Icons -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"
      rel="stylesheet"
    />

    <!-- Charts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3.0.0/dist/chartjs-adapter-date-fns.bundle.min.js"></script>

    <!-- Socket.IO -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.4/socket.io.js"></script>

    <!-- Particles.js -->
    <script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>

    <!-- Three.js for 3D effects -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>

    <style>
      /* === ULTIMATE CSS RESET & BASE === */
      *,
      *::before,
      *::after {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      :root {
        /* === ULTIMATE COLOR SYSTEM === */
        --bg-primary: #0a0e1a;
        --bg-secondary: #1a1f2e;
        --bg-tertiary: #252b3d;
        --bg-card: #1e2332;
        --bg-hover: #2a3142;
        --bg-glass: rgba(30, 35, 50, 0.8);

        /* Neon Accents */
        --neon-primary: #00ff88;
        --neon-secondary: #0099ff;
        --neon-danger: #ff4757;
        --neon-warning: #ffa502;
        --neon-success: #2ed573;
        --neon-purple: #a55eea;
        --neon-pink: #ff3838;
        --neon-cyan: #00d4aa;

        /* Gradients */
        --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --gradient-neon: linear-gradient(135deg, #00ff88 0%, #0099ff 100%);
        --gradient-danger: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        --gradient-success: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --gradient-purple: linear-gradient(135deg, #a55eea 0%, #3742fa 100%);
        --gradient-cyber: linear-gradient(
          135deg,
          #00ff88 0%,
          #00d4aa 50%,
          #0099ff 100%
        );

        /* Text Colors */
        --text-primary: #ffffff;
        --text-secondary: #a0a9c0;
        --text-muted: #6c7293;
        --text-neon: #00ff88;

        /* Shadows & Glows */
        --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
        --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.15);
        --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.25);
        --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.35);
        --glow-primary: 0 0 20px rgba(0, 255, 136, 0.3);
        --glow-secondary: 0 0 20px rgba(0, 153, 255, 0.3);
        --glow-danger: 0 0 20px rgba(255, 71, 87, 0.3);

        /* Animations */
        --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        --transition-bounce: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);

        /* Borders */
        --border-primary: #2a3142;
        --border-neon: #00ff88;
        --border-glass: rgba(255, 255, 255, 0.1);
      }

      html {
        scroll-behavior: smooth;
        overflow-x: hidden;
      }

      body {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
          Roboto, sans-serif;
        background: var(--bg-primary);
        color: var(--text-primary);
        line-height: 1.6;
        overflow-x: hidden;
        position: relative;
        min-height: 100vh;
      }

      /* === PARTICLE BACKGROUND === */
      #particles-js {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        opacity: 0.6;
      }

      /* === GLASSMORPHISM EFFECTS === */
      .glass {
        background: var(--bg-glass);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid var(--border-glass);
      }

      /* === NEON GLOW EFFECTS === */
      .glow-primary {
        box-shadow: var(--glow-primary);
      }

      .glow-secondary {
        box-shadow: var(--glow-secondary);
      }

      .glow-danger {
        box-shadow: var(--glow-danger);
      }

      /* === ANIMATED GRADIENTS === */
      @keyframes gradientShift {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }

      .gradient-animated {
        background: linear-gradient(-45deg, #00ff88, #0099ff, #a55eea, #ff3838);
        background-size: 400% 400%;
        animation: gradientShift 8s ease infinite;
      }

      /* === PULSE ANIMATIONS === */
      @keyframes pulse {
        0%,
        100% {
          transform: scale(1);
          opacity: 1;
        }
        50% {
          transform: scale(1.05);
          opacity: 0.8;
        }
      }

      @keyframes neonPulse {
        0%,
        100% {
          box-shadow: 0 0 5px var(--neon-primary), 0 0 10px var(--neon-primary),
            0 0 15px var(--neon-primary);
        }
        50% {
          box-shadow: 0 0 10px var(--neon-primary), 0 0 20px var(--neon-primary),
            0 0 30px var(--neon-primary);
        }
      }

      .pulse {
        animation: pulse 2s ease-in-out infinite;
      }

      .neon-pulse {
        animation: neonPulse 2s ease-in-out infinite;
      }

      /* === FLOATING ANIMATIONS === */
      @keyframes float {
        0%,
        100% {
          transform: translateY(0px);
        }
        50% {
          transform: translateY(-10px);
        }
      }

      .float {
        animation: float 3s ease-in-out infinite;
      }

      /* === SLIDE IN ANIMATIONS === */
      @keyframes slideInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes slideInLeft {
        from {
          opacity: 0;
          transform: translateX(-30px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      @keyframes slideInRight {
        from {
          opacity: 0;
          transform: translateX(30px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      .slide-in-up {
        animation: slideInUp 0.6s var(--transition-normal) forwards;
      }

      .slide-in-left {
        animation: slideInLeft 0.6s var(--transition-normal) forwards;
      }

      .slide-in-right {
        animation: slideInRight 0.6s var(--transition-normal) forwards;
      }

      /* === LOADING SKELETON === */
      @keyframes shimmer {
        0% {
          background-position: -200px 0;
        }
        100% {
          background-position: calc(200px + 100%) 0;
        }
      }

      .skeleton {
        background: linear-gradient(
          90deg,
          var(--bg-tertiary) 25%,
          var(--bg-hover) 50%,
          var(--bg-tertiary) 75%
        );
        background-size: 200px 100%;
        animation: shimmer 1.5s infinite;
        border-radius: 8px;
      }

      /* === SCROLLBAR STYLING === */
      ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      ::-webkit-scrollbar-track {
        background: var(--bg-secondary);
        border-radius: 4px;
      }

      ::-webkit-scrollbar-thumb {
        background: var(--gradient-neon);
        border-radius: 4px;
        transition: var(--transition-normal);
      }

      ::-webkit-scrollbar-thumb:hover {
        background: var(--gradient-cyber);
        box-shadow: var(--glow-primary);
      }

      /* === UTILITY CLASSES === */
      .container {
        max-width: 1600px;
        margin: 0 auto;
        padding: 0 24px;
      }

      .grid {
        display: grid;
        gap: 24px;
      }

      .grid-cols-1 {
        grid-template-columns: repeat(1, 1fr);
      }
      .grid-cols-2 {
        grid-template-columns: repeat(2, 1fr);
      }
      .grid-cols-3 {
        grid-template-columns: repeat(3, 1fr);
      }
      .grid-cols-4 {
        grid-template-columns: repeat(4, 1fr);
      }
      .grid-cols-6 {
        grid-template-columns: repeat(6, 1fr);
      }

      .flex {
        display: flex;
      }
      .flex-col {
        flex-direction: column;
      }
      .items-center {
        align-items: center;
      }
      .justify-between {
        justify-content: space-between;
      }
      .justify-center {
        justify-content: center;
      }
      .gap-2 {
        gap: 0.5rem;
      }
      .gap-3 {
        gap: 0.75rem;
      }
      .gap-4 {
        gap: 1rem;
      }

      .text-center {
        text-align: center;
      }
      .font-bold {
        font-weight: 700;
      }
      .font-semibold {
        font-weight: 600;
      }
      .text-sm {
        font-size: 0.875rem;
      }
      .text-lg {
        font-size: 1.125rem;
      }
      .text-xl {
        font-size: 1.25rem;
      }
      .text-2xl {
        font-size: 1.5rem;
      }
      .text-3xl {
        font-size: 1.875rem;
      }

      .mb-2 {
        margin-bottom: 0.5rem;
      }
      .mb-4 {
        margin-bottom: 1rem;
      }
      .mb-6 {
        margin-bottom: 1.5rem;
      }
      .mt-4 {
        margin-top: 1rem;
      }
      .p-4 {
        padding: 1rem;
      }
      .p-6 {
        padding: 1.5rem;
      }
      .px-4 {
        padding-left: 1rem;
        padding-right: 1rem;
      }
      .py-2 {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
      }
      .py-3 {
        padding-top: 0.75rem;
        padding-bottom: 0.75rem;
      }

      .rounded {
        border-radius: 8px;
      }
      .rounded-lg {
        border-radius: 12px;
      }
      .rounded-xl {
        border-radius: 16px;
      }
      .rounded-2xl {
        border-radius: 24px;
      }

      .transition {
        transition: var(--transition-normal);
      }
      .transition-fast {
        transition: var(--transition-fast);
      }
      .transition-slow {
        transition: var(--transition-slow);
      }

      /* === LOADING SCREEN === */
      .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--bg-primary);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: opacity 0.5s ease;
      }

      .loading-content {
        text-align: center;
        animation: float 2s ease-in-out infinite;
      }

      .loading-logo {
        font-size: 4rem;
        color: var(--neon-primary);
        margin-bottom: 1rem;
        animation: neonPulse 2s ease-in-out infinite;
      }

      .loading-text {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 2rem;
        background: var(--gradient-cyber);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .loading-bar {
        width: 300px;
        height: 4px;
        background: var(--bg-tertiary);
        border-radius: 2px;
        overflow: hidden;
        position: relative;
      }

      .loading-progress {
        height: 100%;
        background: var(--gradient-neon);
        border-radius: 2px;
        transition: width 0.3s ease;
        position: relative;
      }

      .loading-progress::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.4),
          transparent
        );
        animation: shimmer 1.5s infinite;
      }

      /* === NAVIGATION === */
      .navbar {
        background: var(--bg-glass);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-bottom: 1px solid var(--border-glass);
        padding: 1rem 0;
        position: sticky;
        top: 0;
        z-index: 100;
        transition: var(--transition-normal);
      }

      .navbar.scrolled {
        background: var(--bg-card);
        box-shadow: var(--shadow-lg);
      }

      .navbar-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .navbar-brand {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--text-primary);
        text-decoration: none;
        transition: var(--transition-normal);
      }

      .navbar-brand:hover {
        color: var(--neon-primary);
        text-shadow: 0 0 10px var(--neon-primary);
      }

      .navbar-brand i {
        color: var(--neon-primary);
        animation: pulse 2s ease-in-out infinite;
      }

      .live-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: var(--neon-success);
        animation: neonPulse 2s infinite;
        margin-left: 8px;
      }

      .navbar-actions {
        display: flex;
        align-items: center;
        gap: 16px;
      }

      /* === BUTTONS === */
      .btn {
        padding: 12px 24px;
        border-radius: 12px;
        border: none;
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition-normal);
        display: inline-flex;
        align-items: center;
        gap: 8px;
        text-decoration: none;
        font-size: 0.875rem;
        position: relative;
        overflow: hidden;
      }

      .btn::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        transition: left 0.5s ease;
      }

      .btn:hover::before {
        left: 100%;
      }

      .btn-primary {
        background: var(--gradient-neon);
        color: white;
        box-shadow: var(--glow-primary);
      }

      .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--glow-primary), var(--shadow-lg);
      }

      .btn-outline {
        background: transparent;
        border: 2px solid var(--border-neon);
        color: var(--neon-primary);
      }

      .btn-outline:hover {
        background: var(--neon-primary);
        color: var(--bg-primary);
        box-shadow: var(--glow-primary);
      }

      .btn-glass {
        background: var(--bg-glass);
        backdrop-filter: blur(20px);
        border: 1px solid var(--border-glass);
        color: var(--text-primary);
      }

      .btn-glass:hover {
        background: var(--bg-hover);
        transform: translateY(-2px);
      }

      /* === HERO SECTION === */
      .hero-section {
        padding: 4rem 0;
        background: linear-gradient(
          135deg,
          var(--bg-primary) 0%,
          var(--bg-secondary) 100%
        );
        position: relative;
        overflow: hidden;
      }

      .hero-section::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(
          circle at 50% 50%,
          rgba(0, 255, 136, 0.1) 0%,
          transparent 70%
        );
        pointer-events: none;
      }

      .hero-content {
        text-align: center;
        position: relative;
        z-index: 2;
      }

      .hero-title {
        font-size: 3.5rem;
        font-weight: 900;
        margin-bottom: 1rem;
        line-height: 1.2;
      }

      .gradient-text {
        background: var(--gradient-cyber);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        animation: gradientShift 3s ease infinite;
      }

      .hero-subtitle {
        font-size: 1.25rem;
        color: var(--text-secondary);
        margin-bottom: 3rem;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
      }

      .hero-stats {
        display: flex;
        justify-content: center;
        gap: 3rem;
        flex-wrap: wrap;
      }

      .hero-stat {
        text-align: center;
        padding: 1.5rem;
        background: var(--bg-glass);
        backdrop-filter: blur(20px);
        border-radius: 16px;
        border: 1px solid var(--border-glass);
        min-width: 150px;
        transition: var(--transition-normal);
      }

      .hero-stat:hover {
        transform: translateY(-5px);
        box-shadow: var(--glow-primary);
      }

      .hero-stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--neon-primary);
        margin-bottom: 0.5rem;
      }

      .hero-stat-label {
        font-size: 0.875rem;
        color: var(--text-secondary);
        font-weight: 500;
      }

      /* === FILTER SECTION === */
      .filter-section {
        padding: 2rem 0;
      }

      .filter-card {
        padding: 2rem;
        border-radius: 20px;
        border: 1px solid var(--border-glass);
        transition: var(--transition-normal);
      }

      .filter-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--glow-primary);
      }

      .filter-header {
        margin-bottom: 1.5rem;
      }

      .filter-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .filter-title i {
        color: var(--neon-primary);
      }

      .filter-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        justify-content: center;
      }

      .filter-btn {
        padding: 12px 24px;
        border-radius: 12px;
        border: 2px solid var(--border-primary);
        background: transparent;
        color: var(--text-secondary);
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition-normal);
        position: relative;
        overflow: hidden;
      }

      .filter-btn::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: var(--gradient-neon);
        transition: left 0.3s ease;
        z-index: -1;
      }

      .filter-btn:hover::before,
      .filter-btn.active::before {
        left: 0;
      }

      .filter-btn:hover,
      .filter-btn.active {
        color: white;
        border-color: var(--neon-primary);
        box-shadow: var(--glow-primary);
      }

      /* === NEON CARDS === */
      .neon-card {
        background: var(--bg-card);
        border-radius: 20px;
        padding: 2rem;
        border: 1px solid var(--border-primary);
        position: relative;
        overflow: hidden;
        transition: var(--transition-normal);
        cursor: pointer;
      }

      .neon-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--gradient-neon);
        transform: scaleX(0);
        transition: transform 0.3s ease;
      }

      .neon-card:hover::before {
        transform: scaleX(1);
      }

      .neon-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-xl);
        border-color: var(--neon-primary);
      }

      .neon-card:hover .stat-glow {
        opacity: 1;
        transform: scale(1.1);
      }

      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 1rem;
        background: var(--bg-tertiary);
        color: var(--neon-primary);
        transition: var(--transition-normal);
      }

      .stat-icon.success {
        background: rgba(46, 213, 115, 0.2);
        color: var(--neon-success);
      }

      .stat-icon.info {
        background: rgba(0, 153, 255, 0.2);
        color: var(--neon-secondary);
      }

      .stat-icon.warning {
        background: rgba(255, 165, 2, 0.2);
        color: var(--neon-warning);
      }

      .stat-content {
        position: relative;
        z-index: 2;
      }

      .stat-value {
        font-size: 2.5rem;
        font-weight: 800;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        font-family: "JetBrains Mono", monospace;
      }

      .stat-label {
        font-size: 0.875rem;
        color: var(--text-secondary);
        font-weight: 600;
        margin-bottom: 1rem;
      }

      .stat-change {
        font-size: 0.75rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .stat-change.positive {
        color: var(--neon-success);
      }

      .stat-change.negative {
        color: var(--neon-danger);
      }

      .stat-glow {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 100px;
        height: 100px;
        background: radial-gradient(
          circle,
          var(--neon-primary) 0%,
          transparent 70%
        );
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0;
        transition: var(--transition-slow);
        pointer-events: none;
        z-index: 1;
      }

      .stat-glow.success {
        background: radial-gradient(
          circle,
          var(--neon-success) 0%,
          transparent 70%
        );
      }

      .stat-glow.info {
        background: radial-gradient(
          circle,
          var(--neon-secondary) 0%,
          transparent 70%
        );
      }

      .stat-glow.warning {
        background: radial-gradient(
          circle,
          var(--neon-warning) 0%,
          transparent 70%
        );
      }

      /* === RESPONSIVE DESIGN === */
      @media (max-width: 1200px) {
        .grid-cols-6 {
          grid-template-columns: repeat(3, 1fr);
        }
        .grid-cols-4 {
          grid-template-columns: repeat(2, 1fr);
        }

        .hero-title {
          font-size: 2.5rem;
        }

        .hero-stats {
          gap: 2rem;
        }
      }

      @media (max-width: 768px) {
        .container {
          padding: 0 16px;
        }
        .grid {
          gap: 16px;
        }
        .grid-cols-2,
        .grid-cols-3,
        .grid-cols-4,
        .grid-cols-6 {
          grid-template-columns: 1fr;
        }

        .navbar-brand {
          font-size: 1.25rem;
        }

        .navbar-actions {
          gap: 8px;
        }

        .btn {
          padding: 8px 16px;
          font-size: 0.75rem;
        }

        .loading-bar {
          width: 250px;
        }

        .loading-text {
          font-size: 1.25rem;
        }

        .hero-section {
          padding: 2rem 0;
        }

        .hero-title {
          font-size: 2rem;
        }

        .hero-subtitle {
          font-size: 1rem;
          margin-bottom: 2rem;
        }

        .hero-stats {
          gap: 1rem;
          flex-direction: column;
          align-items: center;
        }

        .hero-stat {
          min-width: 120px;
          padding: 1rem;
        }

        .filter-buttons {
          justify-content: stretch;
        }

        .filter-btn {
          flex: 1;
          min-width: 0;
          padding: 10px 16px;
          font-size: 0.875rem;
        }

        .neon-card {
          padding: 1.5rem;
        }

        .stat-value {
          font-size: 2rem;
        }

        .stat-icon {
          width: 50px;
          height: 50px;
          font-size: 1.25rem;
        }
      }

      @media (max-width: 480px) {
        .hero-title {
          font-size: 1.75rem;
        }

        .navbar-brand span {
          display: none;
        }

        .btn span {
          display: none;
        }

        .filter-buttons {
          flex-direction: column;
        }

        .filter-btn {
          width: 100%;
        }
      }
    </style>
  </head>
  <body>
    <!-- Particle Background -->
    <div id="particles-js"></div>

    <!-- Loading Screen -->
    <div id="loadingScreen" class="loading-screen">
      <div class="loading-content">
        <div class="loading-logo">
          <i class="fas fa-chart-line"></i>
        </div>
        <div class="loading-text">Ultimate Trading Dashboard</div>
        <div class="loading-bar">
          <div class="loading-progress"></div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div id="mainContent" style="opacity: 0">
      <!-- Navigation -->
      <nav class="navbar" id="navbar">
        <div class="container">
          <div class="navbar-content">
            <a href="#" class="navbar-brand">
              <i class="fas fa-chart-line"></i>
              <span>Ultimate Trading Dashboard</span>
              <div class="live-indicator" id="liveIndicator"></div>
              <span class="text-sm" style="color: var(--neon-success)"
                >Live</span
              >
            </a>

            <div class="navbar-actions">
              <button class="btn btn-glass" onclick="toggleTheme()">
                <i class="fas fa-palette"></i>
                <span>Theme</span>
              </button>
              <button class="btn btn-outline" onclick="toggleFullscreen()">
                <i class="fas fa-expand"></i>
                <span>Fullscreen</span>
              </button>
              <button class="btn btn-primary" onclick="refreshData()">
                <i class="fas fa-sync-alt"></i>
                <span>Refresh</span>
              </button>
            </div>
          </div>
        </div>
      </nav>

      <!-- Hero Section -->
      <section class="hero-section">
        <div class="container">
          <div class="hero-content slide-in-up">
            <h1 class="hero-title">
              <span class="gradient-text">Ultimate</span> Trading Dashboard
            </h1>
            <p class="hero-subtitle">
              Real-time Discord signal monitoring with advanced analytics
            </p>
            <div class="hero-stats">
              <div class="hero-stat">
                <div class="hero-stat-value" id="heroTotalSignals">-</div>
                <div class="hero-stat-label">Total Signals</div>
              </div>
              <div class="hero-stat">
                <div class="hero-stat-value" id="heroWinRate">-</div>
                <div class="hero-stat-label">Win Rate</div>
              </div>
              <div class="hero-stat">
                <div class="hero-stat-value" id="heroTotalPnl">-</div>
                <div class="hero-stat-label">Total PnL</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Time Filter Section -->
      <section class="filter-section">
        <div class="container">
          <div class="filter-card glass slide-in-left">
            <div class="filter-header">
              <h3 class="filter-title">
                <i class="fas fa-clock"></i>
                Analysis Period
              </h3>
            </div>
            <div class="filter-buttons" id="timeFilterButtons">
              <button class="filter-btn active" onclick="setTimeFilter(null)">
                All Time
              </button>
              <button class="filter-btn" onclick="setTimeFilter(1)">24h</button>
              <button class="filter-btn" onclick="setTimeFilter(7)">
                7 days
              </button>
              <button class="filter-btn" onclick="setTimeFilter(30)">
                30 days
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- Main Dashboard Content -->
      <main class="dashboard-main">
        <div class="container">
          <!-- Statistics Grid -->
          <section class="stats-section mb-6">
            <div class="grid grid-cols-4">
              <div
                class="stat-card neon-card slide-in-up"
                style="animation-delay: 0.1s"
              >
                <div class="stat-icon">
                  <i class="fas fa-signal"></i>
                </div>
                <div class="stat-content">
                  <div class="stat-value" id="totalSignals">-</div>
                  <div class="stat-label">Total Signals</div>
                  <div class="stat-change positive" id="totalSignalsChange">
                    <i class="fas fa-arrow-up"></i> +12% vs previous
                  </div>
                </div>
                <div class="stat-glow"></div>
              </div>

              <div
                class="stat-card neon-card slide-in-up"
                style="animation-delay: 0.2s"
              >
                <div class="stat-icon success">
                  <i class="fas fa-trophy"></i>
                </div>
                <div class="stat-content">
                  <div class="stat-value" id="winRate">-</div>
                  <div class="stat-label">Win Rate</div>
                  <div class="stat-change positive" id="winRateChange">
                    <i class="fas fa-arrow-up"></i> +2.4% vs previous
                  </div>
                </div>
                <div class="stat-glow success"></div>
              </div>

              <div
                class="stat-card neon-card slide-in-up"
                style="animation-delay: 0.3s"
              >
                <div class="stat-icon info">
                  <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-content">
                  <div class="stat-value" id="totalPnl">-</div>
                  <div class="stat-label">Total PnL</div>
                  <div class="stat-change positive" id="totalPnlChange">
                    <i class="fas fa-arrow-up"></i> +15.7% vs previous
                  </div>
                </div>
                <div class="stat-glow info"></div>
              </div>

              <div
                class="stat-card neon-card slide-in-up"
                style="animation-delay: 0.4s"
              >
                <div class="stat-icon warning">
                  <i class="fas fa-chart-bar"></i>
                </div>
                <div class="stat-content">
                  <div class="stat-value" id="avgPnl">-</div>
                  <div class="stat-label">Average PnL</div>
                  <div class="stat-change negative" id="avgPnlChange">
                    <i class="fas fa-arrow-down"></i> -0.8% vs previous
                  </div>
                </div>
                <div class="stat-glow warning"></div>
              </div>
            </div>
          </section>
        </div>
      </main>
    </div>

    <script>
      // === GLOBAL VARIABLES ===
      const socket = io();
      let currentTimeFilter = null;
      let currentTheme = "dark";
      let isFullscreen = false;

      // === INITIALIZATION ===
      document.addEventListener("DOMContentLoaded", function () {
        console.log("🚀 Initializing Ultimate Trading Dashboard...");

        initParticles();
        initLoadingScreen();
        initScrollEffects();
        initKeyboardShortcuts();

        // Initialize after loading screen
        setTimeout(() => {
          initDashboard();
        }, 2000);
      });

      // === PARTICLE SYSTEM ===
      function initParticles() {
        particlesJS("particles-js", {
          particles: {
            number: { value: 80, density: { enable: true, value_area: 800 } },
            color: { value: "#00ff88" },
            shape: { type: "circle" },
            opacity: { value: 0.5, random: false },
            size: { value: 3, random: true },
            line_linked: {
              enable: true,
              distance: 150,
              color: "#00ff88",
              opacity: 0.4,
              width: 1,
            },
            move: {
              enable: true,
              speed: 2,
              direction: "none",
              random: false,
              straight: false,
              out_mode: "out",
              bounce: false,
            },
          },
          interactivity: {
            detect_on: "canvas",
            events: {
              onhover: { enable: true, mode: "repulse" },
              onclick: { enable: true, mode: "push" },
              resize: true,
            },
          },
          retina_detect: true,
        });
      }

      // === LOADING SCREEN ===
      function initLoadingScreen() {
        const loadingScreen = document.getElementById("loadingScreen");
        const mainContent = document.getElementById("mainContent");
        const progress = document.querySelector(".loading-progress");

        let width = 0;
        const interval = setInterval(() => {
          width += Math.random() * 15;
          if (width >= 100) {
            width = 100;
            clearInterval(interval);
            setTimeout(() => {
              loadingScreen.style.opacity = "0";
              setTimeout(() => {
                loadingScreen.style.display = "none";
                mainContent.style.opacity = "1";
                mainContent.style.transition = "opacity 0.5s ease";

                // Trigger entrance animations
                triggerEntranceAnimations();
              }, 500);
            }, 500);
          }
          progress.style.width = width + "%";
        }, 100);
      }

      // === SCROLL EFFECTS ===
      function initScrollEffects() {
        const navbar = document.getElementById("navbar");

        window.addEventListener("scroll", () => {
          if (window.scrollY > 50) {
            navbar.classList.add("scrolled");
          } else {
            navbar.classList.remove("scrolled");
          }
        });
      }

      // === KEYBOARD SHORTCUTS ===
      function initKeyboardShortcuts() {
        document.addEventListener("keydown", (e) => {
          // Ctrl/Cmd + R - Refresh data
          if ((e.ctrlKey || e.metaKey) && e.key === "r") {
            e.preventDefault();
            refreshData();
          }

          // F11 - Toggle fullscreen
          if (e.key === "F11") {
            e.preventDefault();
            toggleFullscreen();
          }

          // Ctrl/Cmd + T - Toggle theme
          if ((e.ctrlKey || e.metaKey) && e.key === "t") {
            e.preventDefault();
            toggleTheme();
          }
        });
      }

      // === ENTRANCE ANIMATIONS ===
      function triggerEntranceAnimations() {
        const elements = document.querySelectorAll(
          ".slide-in-up, .slide-in-left, .slide-in-right"
        );

        elements.forEach((element, index) => {
          setTimeout(() => {
            element.style.opacity = "1";
            element.style.transform = "translateY(0) translateX(0)";
          }, index * 100);
        });
      }

      // === DASHBOARD INITIALIZATION ===
      function initDashboard() {
        console.log("📊 Initializing dashboard data...");
        loadData();
        setupWebSocket();

        // Auto-refresh every 30 seconds
        setInterval(loadData, 30000);
      }

      // === DATA LOADING ===
      async function loadData() {
        try {
          showNotification("Loading data...", "info", 2000);

          const [statsResponse] = await Promise.all([
            fetch(
              `/api/statistics${
                currentTimeFilter ? `?days=${currentTimeFilter}` : ""
              }`
            ),
          ]);

          const stats = await statsResponse.json();
          updateStatistics(stats);

          console.log("✅ Data loaded successfully");
        } catch (error) {
          console.error("❌ Error loading data:", error);
          showNotification("Error loading data", "error");
        }
      }

      // === UPDATE FUNCTIONS ===
      function updateStatistics(stats) {
        // Main stats
        updateElement("totalSignals", stats.total_signals || 0);
        updateElement("winRate", `${(stats.win_rate || 0).toFixed(1)}%`);
        updateElement(
          "totalPnl",
          `${((stats.total_pnl || 0) * 100).toFixed(2)}%`
        );
        updateElement("avgPnl", `${((stats.avg_pnl || 0) * 100).toFixed(2)}%`);

        // Hero stats
        updateElement("heroTotalSignals", stats.total_signals || 0);
        updateElement("heroWinRate", `${(stats.win_rate || 0).toFixed(1)}%`);
        updateElement(
          "heroTotalPnl",
          `${((stats.total_pnl || 0) * 100).toFixed(2)}%`
        );

        // Animate counters
        animateCounters();
      }

      function updateElement(id, value) {
        const element = document.getElementById(id);
        if (element) {
          element.textContent = value;
        }
      }

      // === ANIMATIONS ===
      function animateCounters() {
        const counters = document.querySelectorAll(".stat-value");

        counters.forEach((counter) => {
          const target =
            parseInt(counter.textContent.replace(/[^\d]/g, "")) || 0;
          let current = 0;
          const increment = target / 50;

          const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
              current = target;
              clearInterval(timer);
            }

            const suffix = counter.textContent.replace(/[\d]/g, "");
            counter.textContent = Math.floor(current) + suffix;
          }, 20);
        });
      }

      // === EVENT HANDLERS ===
      function setTimeFilter(days) {
        currentTimeFilter = days;

        // Update button states
        document.querySelectorAll(".filter-btn").forEach((btn) => {
          btn.classList.remove("active");
        });
        event.target.classList.add("active");

        // Reload data
        loadData();
        showNotification(
          `Filter set to ${days ? `${days} days` : "all time"}`,
          "success",
          2000
        );
      }

      function refreshData() {
        showNotification("Refreshing data...", "info", 2000);
        loadData();

        // Add refresh animation to button
        const btn = event.target.closest(".btn");
        const icon = btn.querySelector("i");
        icon.style.animation = "spin 1s linear";
        setTimeout(() => {
          icon.style.animation = "";
        }, 1000);
      }

      function toggleTheme() {
        currentTheme = currentTheme === "dark" ? "light" : "dark";
        document.body.classList.toggle("light-theme");
        showNotification(`Switched to ${currentTheme} theme`, "info", 2000);
      }

      function toggleFullscreen() {
        if (!isFullscreen) {
          if (document.documentElement.requestFullscreen) {
            document.documentElement.requestFullscreen();
          }
        } else {
          if (document.exitFullscreen) {
            document.exitFullscreen();
          }
        }
        isFullscreen = !isFullscreen;
      }

      // === WEBSOCKET ===
      function setupWebSocket() {
        socket.on("connect", function () {
          console.log("🔗 Connected to server");
          document.getElementById("liveIndicator").style.background =
            "var(--neon-success)";
          showNotification("Connected to live data", "success", 3000);
        });

        socket.on("disconnect", function () {
          console.log("❌ Disconnected from server");
          document.getElementById("liveIndicator").style.background =
            "var(--neon-danger)";
          showNotification("Disconnected from server", "error");
        });

        socket.on("new_signal", function (data) {
          showNotification(`New signal: ${data.pair} ${data.side}`, "success");
          loadData();
        });

        socket.on("signal_update", function (data) {
          showNotification(
            `Signal updated: ${data.pair} - ${data.status}`,
            "info"
          );
          loadData();
        });
      }

      // === NOTIFICATIONS ===
      function showNotification(message, type = "info", duration = 5000) {
        const notification = document.createElement("div");
        notification.className = `notification ${type}`;
        notification.innerHTML = `
          <div style="display: flex; align-items: center; justify-content: space-between;">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()"
                    style="background: none; border: none; color: white; cursor: pointer; margin-left: 1rem;">
              <i class="fas fa-times"></i>
            </button>
          </div>
        `;

        // Notification styles
        Object.assign(notification.style, {
          position: "fixed",
          top: "20px",
          right: "20px",
          padding: "16px 20px",
          borderRadius: "12px",
          color: "white",
          fontWeight: "500",
          zIndex: "10000",
          minWidth: "300px",
          boxShadow: "0 8px 25px rgba(0, 0, 0, 0.25)",
          transform: "translateX(100%)",
          transition: "transform 0.3s ease",
          backdropFilter: "blur(20px)",
        });

        // Type-specific colors
        const colors = {
          success: "var(--neon-success)",
          error: "var(--neon-danger)",
          warning: "var(--neon-warning)",
          info: "var(--neon-secondary)",
        };

        notification.style.background = colors[type] || colors.info;

        document.body.appendChild(notification);

        // Slide in
        setTimeout(() => {
          notification.style.transform = "translateX(0)";
        }, 100);

        // Auto remove
        setTimeout(() => {
          notification.style.transform = "translateX(100%)";
          setTimeout(() => {
            if (notification.parentNode) {
              notification.parentNode.removeChild(notification);
            }
          }, 300);
        }, duration);
      }

      // === UTILITY FUNCTIONS ===
      // Add spin animation for refresh button
      const style = document.createElement("style");
      style.textContent = `
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }

        .notification {
          animation: slideInRight 0.3s ease;
        }

        @keyframes slideInRight {
          from { transform: translateX(100%); }
          to { transform: translateX(0); }
        }
      `;
      document.head.appendChild(style);

      console.log("🎉 Ultimate Trading Dashboard loaded successfully!");
    </script>
  </body>
</html>
